import React from 'react';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { CalendarIcon, ClockIcon, ArrowRightIcon } from '@heroicons/react/24/outline';
import PageTransition from '../components/PageTransition';

const Articles = () => {
  const [ref, inView] = useInView({
    threshold: 0.1,
    triggerOnce: true
  });

  const articles = [
    {
      id: 1,
      title: "Building Scalable React Applications with Modern Architecture",
      excerpt: "Learn how to structure large React applications using modern patterns, state management solutions, and performance optimization techniques.",
      date: "2024-01-15",
      readTime: "8 min read",
      category: "React",
      featured: true
    },
    {
      id: 2,
      title: "The Future of Web Development: Trends to Watch in 2024",
      excerpt: "Explore the latest trends in web development including AI integration, WebAssembly, and the evolution of JavaScript frameworks.",
      date: "2024-01-10",
      readTime: "6 min read",
      category: "Web Development",
      featured: true
    },
    {
      id: 3,
      title: "Mastering CSS Grid and Flexbox for Modern Layouts",
      excerpt: "A comprehensive guide to creating responsive, flexible layouts using CSS Grid and Flexbox with practical examples.",
      date: "2024-01-05",
      readTime: "10 min read",
      category: "CSS",
      featured: false
    },
    {
      id: 4,
      title: "Node.js Performance Optimization: Best Practices",
      excerpt: "Discover techniques to optimize your Node.js applications for better performance, scalability, and resource utilization.",
      date: "2023-12-28",
      readTime: "7 min read",
      category: "Node.js",
      featured: false
    },
    {
      id: 5,
      title: "Introduction to TypeScript for JavaScript Developers",
      excerpt: "Get started with TypeScript and learn how it can improve your JavaScript development experience with type safety.",
      date: "2023-12-20",
      readTime: "9 min read",
      category: "TypeScript",
      featured: false
    },
    {
      id: 6,
      title: "Building RESTful APIs with Express.js and MongoDB",
      excerpt: "Step-by-step guide to creating robust RESTful APIs using Express.js, MongoDB, and modern authentication techniques.",
      date: "2023-12-15",
      readTime: "12 min read",
      category: "Backend",
      featured: false
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3
      }
    }
  };

  const cardVariants = {
    hidden: { 
      opacity: 0, 
      y: 50,
      scale: 0.9
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  };

  const ArticleCard = ({ article, index }) => (
    <motion.article
      variants={cardVariants}
      whileHover={{ 
        y: -10,
        scale: 1.02,
        transition: { duration: 0.3 }
      }}
      className={`
        group cursor-pointer
        ${article.featured ? 'lg:col-span-2' : ''}
      `}
    >
      <div className="glass-effect rounded-2xl overflow-hidden border border-white/10 hover:border-primary-400/50 transition-all duration-300 h-full">
        {/* Article Header */}
        <div className="p-6 pb-4">
          <div className="flex items-center justify-between mb-4">
            <span className="px-3 py-1 text-xs font-semibold bg-gradient-to-r from-primary-500/20 to-secondary-500/20 text-primary-300 rounded-full border border-primary-400/30">
              {article.category}
            </span>
            {article.featured && (
              <span className="px-3 py-1 text-xs font-semibold bg-gradient-to-r from-accent-500 to-accent-600 text-white rounded-full">
                Featured
              </span>
            )}
          </div>

          <h3 className="text-xl font-bold text-white mb-3 group-hover:text-primary-400 transition-colors leading-tight">
            {article.title}
          </h3>

          <p className="text-gray-300 mb-4 leading-relaxed">
            {article.excerpt}
          </p>

          <div className="flex items-center justify-between text-sm text-gray-400">
            <div className="flex items-center space-x-4">
              <div className="flex items-center">
                <CalendarIcon className="w-4 h-4 mr-1" />
                {new Date(article.date).toLocaleDateString('en-US', { 
                  year: 'numeric', 
                  month: 'short', 
                  day: 'numeric' 
                })}
              </div>
              <div className="flex items-center">
                <ClockIcon className="w-4 h-4 mr-1" />
                {article.readTime}
              </div>
            </div>

            <motion.div
              className="flex items-center text-primary-400 group-hover:text-primary-300 transition-colors"
              whileHover={{ x: 5 }}
            >
              Read More
              <ArrowRightIcon className="w-4 h-4 ml-1" />
            </motion.div>
          </div>
        </div>

        {/* Animated bottom border */}
        <div className="h-1 bg-gradient-to-r from-primary-500 via-secondary-500 to-accent-500 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left" />
      </div>
    </motion.article>
  );

  return (
    <PageTransition variant="blur">
      <div className="min-h-screen py-20 px-4 sm:px-6 lg:px-8">
        {/* Background Effects */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-1/4 left-1/3 w-96 h-96 bg-secondary-500/10 rounded-full blur-3xl animate-pulse-slow" />
          <div className="absolute bottom-1/3 right-1/3 w-96 h-96 bg-accent-500/10 rounded-full blur-3xl animate-pulse-slow" style={{ animationDelay: '2s' }} />
        </div>

        <div className="max-w-7xl mx-auto relative z-10">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: -50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold mb-6">
              <span className="gradient-text">Featured Articles</span>
            </h1>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
              Insights, tutorials, and thoughts on web development, technology trends, 
              and best practices from my journey as a developer.
            </p>
            <div className="w-24 h-1 bg-gradient-to-r from-primary-400 to-secondary-400 rounded-full mx-auto mt-8" />
          </motion.div>

          {/* Articles Grid */}
          <motion.div
            ref={ref}
            variants={containerVariants}
            initial="hidden"
            animate={inView ? "visible" : "hidden"}
            className="grid grid-cols-1 lg:grid-cols-3 gap-8"
          >
            {articles.map((article, index) => (
              <ArticleCard key={article.id} article={article} index={index} />
            ))}
          </motion.div>

          {/* Call to Action */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
            transition={{ duration: 0.8, delay: 1 }}
            className="text-center mt-16"
          >
            <p className="text-gray-300 mb-6">
              Want to stay updated with my latest articles and insights?
            </p>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="inline-flex items-center px-6 py-3 text-lg font-semibold text-white bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full hover:from-primary-600 hover:to-secondary-600 transition-all duration-300 neon-glow"
            >
              Subscribe to Newsletter
              <motion.span
                animate={{ x: [0, 5, 0] }}
                transition={{ repeat: Infinity, duration: 1.5 }}
                className="ml-2"
              >
                →
              </motion.span>
            </motion.button>
          </motion.div>
        </div>
      </div>
    </PageTransition>
  );
};

export default Articles;
