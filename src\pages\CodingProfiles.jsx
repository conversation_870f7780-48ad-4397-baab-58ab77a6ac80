import React from 'react';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { ArrowTopRightOnSquareIcon } from '@heroicons/react/24/outline';
import PageTransition from '../components/PageTransition';

const CodingProfiles = () => {
  const [ref, inView] = useInView({
    threshold: 0.1,
    triggerOnce: true
  });

  const platforms = [
    {
      name: "LeetCode",
      description: "Algorithmic problem solving and coding interview preparation",
      url: "https://leetcode.com/yourusername",
      stats: { solved: "500+", rank: "Top 10%" },
      color: "from-orange-400 to-orange-600",
      logo: "🟠"
    },
    {
      name: "Codeforces",
      description: "Competitive programming contests and problem solving",
      url: "https://codeforces.com/profile/yourusername",
      stats: { rating: "1800+", rank: "Expert" },
      color: "from-blue-400 to-blue-600",
      logo: "🔵"
    },
    {
      name: "Codewars",
      description: "Coding challenges and kata to improve programming skills",
      url: "https://www.codewars.com/users/yourusername",
      stats: { kyu: "4 kyu", honor: "2000+" },
      color: "from-red-400 to-red-600",
      logo: "🔴"
    },
    {
      name: "HackerRank",
      description: "Programming challenges and skill assessments",
      url: "https://www.hackerrank.com/yourusername",
      stats: { stars: "5 ⭐", badges: "20+" },
      color: "from-green-400 to-green-600",
      logo: "🟢"
    },
    {
      name: "GitHub",
      description: "Open source contributions and personal projects",
      url: "https://github.com/yourusername",
      stats: { repos: "50+", stars: "200+" },
      color: "from-gray-400 to-gray-600",
      logo: "⚫"
    },
    {
      name: "Stack Overflow",
      description: "Helping developers solve problems and sharing knowledge",
      url: "https://stackoverflow.com/users/yourid",
      stats: { reputation: "5k+", answers: "100+" },
      color: "from-yellow-400 to-orange-500",
      logo: "🟡"
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3
      }
    }
  };

  const cardVariants = {
    hidden: { 
      opacity: 0, 
      y: 50,
      rotateY: -30
    },
    visible: {
      opacity: 1,
      y: 0,
      rotateY: 0,
      transition: {
        duration: 0.8,
        ease: "easeOut"
      }
    }
  };

  const PlatformCard = ({ platform, index }) => (
    <motion.a
      href={platform.url}
      target="_blank"
      rel="noopener noreferrer"
      variants={cardVariants}
      whileHover={{ 
        y: -15,
        rotateY: 10,
        scale: 1.05,
        transition: { duration: 0.3 }
      }}
      whileTap={{ scale: 0.95 }}
      className="block group cursor-pointer transform-gpu"
      style={{ transformStyle: 'preserve-3d' }}
    >
      <div className="glass-effect rounded-2xl p-6 border border-white/10 hover:border-primary-400/50 transition-all duration-300 h-full relative overflow-hidden">
        {/* Background Gradient */}
        <div className={`absolute inset-0 bg-gradient-to-br ${platform.color} opacity-5 group-hover:opacity-10 transition-opacity duration-300`} />
        
        {/* Logo and External Link Icon */}
        <div className="flex items-center justify-between mb-4 relative z-10">
          <motion.div
            whileHover={{ 
              scale: 1.2,
              rotate: 360,
              transition: { duration: 0.5 }
            }}
            className="text-4xl"
          >
            {platform.logo}
          </motion.div>
          <motion.div
            whileHover={{ scale: 1.1 }}
            className="p-2 rounded-full bg-white/10 group-hover:bg-primary-500/20 transition-colors duration-300"
          >
            <ArrowTopRightOnSquareIcon className="w-5 h-5 text-gray-400 group-hover:text-blue-400 transition-colors" />
          </motion.div>
        </div>

        {/* Platform Name */}
        <h3 className="text-2xl font-bold text-white mb-3 group-hover:text-primary-400 transition-colors">
          {platform.name}
        </h3>

        {/* Description */}
        <p className="text-gray-300 mb-6 leading-relaxed">
          {platform.description}
        </p>

        {/* Stats */}
        <div className="space-y-3">
          {Object.entries(platform.stats).map(([key, value]) => (
            <div key={key} className="flex items-center justify-between">
              <span className="text-gray-400 capitalize">{key}:</span>
              <span className={`font-semibold bg-gradient-to-r ${platform.color} bg-clip-text text-transparent`}>
                {value}
              </span>
            </div>
          ))}
        </div>

        {/* Hover Effect Overlay */}
        <div className="absolute inset-0 bg-gradient-to-t from-primary-500/10 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

        {/* 3D Shadow Effect */}
        <div className={`absolute inset-0 rounded-2xl bg-gradient-to-br ${platform.color} opacity-20 transform translate-x-2 translate-y-2 -z-10 group-hover:opacity-30 transition-opacity duration-300`} />
      </div>
    </motion.a>
  );

  return (
    <PageTransition variant="scale">
      <div className="min-h-screen py-20 px-4 sm:px-6 lg:px-8">
        {/* Background Effects */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-1/4 right-1/4 w-96 h-96 bg-primary-500/10 rounded-full blur-3xl animate-pulse-slow" />
          <div className="absolute bottom-1/4 left-1/4 w-96 h-96 bg-secondary-500/10 rounded-full blur-3xl animate-pulse-slow" style={{ animationDelay: '1.5s' }} />
        </div>

        <div className="max-w-6xl mx-auto relative z-10">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: -50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold mb-6">
              <span className="gradient-text">Coding Profiles</span>
            </h1>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
              Explore my coding journey across various platforms where I solve problems, 
              contribute to open source, and continuously improve my programming skills.
            </p>
            <div className="w-24 h-1 bg-gradient-to-r from-primary-400 to-secondary-400 rounded-full mx-auto mt-8" />
          </motion.div>

          {/* Platforms Grid */}
          <motion.div
            ref={ref}
            variants={containerVariants}
            initial="hidden"
            animate={inView ? "visible" : "hidden"}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          >
            {platforms.map((platform, index) => (
              <PlatformCard key={platform.name} platform={platform} index={index} />
            ))}
          </motion.div>

          {/* Additional Info */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
            transition={{ duration: 0.8, delay: 1.5 }}
            className="text-center mt-16"
          >
            <div className="glass-effect rounded-2xl p-8 border border-white/10">
              <h3 className="text-2xl font-bold text-white mb-4">Competitive Programming Journey</h3>
              <p className="text-gray-300 leading-relaxed mb-6">
                I believe in continuous learning and challenging myself with algorithmic problems. 
                These platforms have helped me develop strong problem-solving skills and stay sharp 
                with data structures and algorithms.
              </p>
              
              <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                {[
                  { label: 'Problems Solved', value: '1000+' },
                  { label: 'Contest Participations', value: '50+' },
                  { label: 'Languages Used', value: '5+' },
                  { label: 'Years Active', value: '3+' }
                ].map((stat, index) => (
                  <motion.div
                    key={stat.label}
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: 2 + index * 0.1 }}
                    className="text-center"
                  >
                    <div className="text-2xl font-bold gradient-text mb-1">{stat.value}</div>
                    <div className="text-sm text-gray-400">{stat.label}</div>
                  </motion.div>
                ))}
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </PageTransition>
  );
};

export default CodingProfiles;
