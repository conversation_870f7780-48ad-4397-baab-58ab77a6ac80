import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { DocumentArrowDownIcon, EyeIcon, XMarkIcon } from '@heroicons/react/24/outline';
import PageTransition from '../components/PageTransition';

const Resume = () => {
  const [showModal, setShowModal] = useState(false);

  const buttonVariants = {
    hover: {
      scale: 1.05,
      boxShadow: "0 20px 40px rgba(59, 130, 246, 0.3)",
      transition: { duration: 0.3 }
    },
    tap: { scale: 0.95 }
  };

  const modalVariants = {
    hidden: { 
      opacity: 0, 
      scale: 0.8,
      rotateX: -15
    },
    visible: { 
      opacity: 1, 
      scale: 1,
      rotateX: 0,
      transition: {
        duration: 0.5,
        ease: "easeOut"
      }
    },
    exit: { 
      opacity: 0, 
      scale: 0.8,
      rotateX: 15,
      transition: {
        duration: 0.3
      }
    }
  };

  return (
    <PageTransition variant="blur">
      <div className="min-h-screen py-20 px-4 sm:px-6 lg:px-8 flex items-center justify-center">
        {/* Background Effects */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-primary-500/15 rounded-full blur-3xl animate-pulse-slow" />
          <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-secondary-500/15 rounded-full blur-3xl animate-pulse-slow" style={{ animationDelay: '2s' }} />
        </div>

        <div className="max-w-4xl mx-auto text-center relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold mb-8">
              <span className="gradient-text">My Resume</span>
            </h1>
            
            <p className="text-xl text-gray-300 max-w-2xl mx-auto mb-12 leading-relaxed">
              Download my resume to learn more about my experience, skills, and achievements.
            </p>

            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
              {/* Download Button */}
              <motion.button
                variants={buttonVariants}
                whileHover="hover"
                whileTap="tap"
                className="inline-flex items-center px-8 py-4 text-lg font-semibold text-white bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full hover:from-primary-600 hover:to-secondary-600 transition-all duration-300 neon-glow"
              >
                <DocumentArrowDownIcon className="w-6 h-6 mr-3" />
                Download Resume
                <motion.div
                  animate={{ y: [0, 3, 0] }}
                  transition={{ repeat: Infinity, duration: 1.5 }}
                  className="ml-2"
                >
                  ↓
                </motion.div>
              </motion.button>

              {/* Preview Button */}
              <motion.button
                variants={buttonVariants}
                whileHover="hover"
                whileTap="tap"
                onClick={() => setShowModal(true)}
                className="inline-flex items-center px-8 py-4 text-lg font-semibold text-white border-2 border-white/20 rounded-full hover:border-primary-400 hover:bg-white/5 transition-all duration-300 glass-effect"
              >
                <EyeIcon className="w-6 h-6 mr-3" />
                Preview Resume
              </motion.button>
            </div>

            {/* Quick Stats */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.5 }}
              className="grid grid-cols-2 md:grid-cols-4 gap-6 mt-16"
            >
              {[
                { label: 'Years Experience', value: '5+' },
                { label: 'Projects Completed', value: '50+' },
                { label: 'Technologies', value: '20+' },
                { label: 'Happy Clients', value: '30+' }
              ].map((stat, index) => (
                <motion.div
                  key={stat.label}
                  whileHover={{ scale: 1.05, y: -5 }}
                  className="glass-effect rounded-xl p-6 border border-white/10"
                >
                  <div className="text-3xl font-bold gradient-text mb-2">{stat.value}</div>
                  <div className="text-gray-400 text-sm">{stat.label}</div>
                </motion.div>
              ))}
            </motion.div>
          </motion.div>
        </div>

        {/* Resume Preview Modal */}
        <AnimatePresence>
          {showModal && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
              onClick={() => setShowModal(false)}
            >
              <motion.div
                variants={modalVariants}
                initial="hidden"
                animate="visible"
                exit="exit"
                className="bg-dark-800 rounded-2xl border border-white/20 max-w-4xl w-full max-h-[90vh] overflow-hidden"
                onClick={(e) => e.stopPropagation()}
              >
                {/* Modal Header */}
                <div className="flex items-center justify-between p-6 border-b border-white/10">
                  <h3 className="text-xl font-semibold text-white">Resume Preview</h3>
                  <motion.button
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    onClick={() => setShowModal(false)}
                    className="p-2 text-gray-400 hover:text-white transition-colors"
                  >
                    <XMarkIcon className="w-6 h-6" />
                  </motion.button>
                </div>

                {/* Modal Content */}
                <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
                  <div className="bg-white text-black rounded-lg p-8 shadow-2xl">
                    {/* Resume Content Placeholder */}
                    <div className="text-center mb-8">
                      <h1 className="text-3xl font-bold mb-2">Your Name</h1>
                      <p className="text-gray-600">Full Stack Developer</p>
                      <p className="text-sm text-gray-500"><EMAIL> | +1 (555) 123-4567</p>
                    </div>

                    <div className="space-y-6">
                      <section>
                        <h2 className="text-xl font-bold border-b-2 border-gray-300 pb-2 mb-4">Experience</h2>
                        <div className="space-y-4">
                          <div>
                            <h3 className="font-semibold">Senior Full Stack Developer</h3>
                            <p className="text-gray-600">Company Name • 2022 - Present</p>
                            <ul className="list-disc list-inside text-sm text-gray-700 mt-2">
                              <li>Led development of multiple web applications using React and Node.js</li>
                              <li>Improved application performance by 40% through optimization techniques</li>
                              <li>Mentored junior developers and conducted code reviews</li>
                            </ul>
                          </div>
                        </div>
                      </section>

                      <section>
                        <h2 className="text-xl font-bold border-b-2 border-gray-300 pb-2 mb-4">Education</h2>
                        <div>
                          <h3 className="font-semibold">Bachelor of Computer Science</h3>
                          <p className="text-gray-600">University Name • 2018 - 2022</p>
                        </div>
                      </section>

                      <section>
                        <h2 className="text-xl font-bold border-b-2 border-gray-300 pb-2 mb-4">Skills</h2>
                        <div className="flex flex-wrap gap-2">
                          {['React', 'Node.js', 'JavaScript', 'TypeScript', 'Python', 'MongoDB'].map(skill => (
                            <span key={skill} className="px-3 py-1 bg-gray-200 text-gray-700 rounded-full text-sm">
                              {skill}
                            </span>
                          ))}
                        </div>
                      </section>
                    </div>
                  </div>
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </PageTransition>
  );
};

export default Resume;
