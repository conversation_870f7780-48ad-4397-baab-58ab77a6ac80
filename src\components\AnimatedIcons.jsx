import React from 'react';
import { motion } from 'framer-motion';
import { 
  CodeBracketIcon, 
  CubeIcon, 
  SparklesIcon,
  RocketLaunchIcon,
  HandRaisedIcon
} from '@heroicons/react/24/outline';

const AnimatedIcons = () => {
  const icons = [
    {
      Icon: CubeIcon,
      delay: 0,
      color: 'text-blue-400',
      animation: 'rotate'
    },
    {
      Icon: CodeBracketIcon,
      delay: 0.2,
      color: 'text-purple-400',
      animation: 'bounce'
    },
    {
      Icon: SparklesIcon,
      delay: 0.4,
      color: 'text-pink-400',
      animation: 'pulse'
    },
    {
      Icon: RocketLaunchIcon,
      delay: 0.6,
      color: 'text-blue-300',
      animation: 'float'
    },
    { 
      Icon: HandRaisedIcon, 
      delay: 0.8, 
      color: 'text-yellow-400',
      animation: 'wave'
    }
  ];

  const getAnimationProps = (animation) => {
    switch (animation) {
      case 'rotate':
        return {
          animate: { 
            rotateY: [0, 360],
            rotateX: [0, 15, 0]
          },
          transition: { 
            duration: 4, 
            repeat: Infinity, 
            ease: "linear" 
          }
        };
      case 'bounce':
        return {
          animate: { 
            y: [0, -20, 0],
            rotateZ: [0, 5, -5, 0]
          },
          transition: { 
            duration: 2, 
            repeat: Infinity, 
            ease: "easeInOut" 
          }
        };
      case 'pulse':
        return {
          animate: { 
            scale: [1, 1.2, 1],
            opacity: [0.7, 1, 0.7]
          },
          transition: { 
            duration: 2, 
            repeat: Infinity, 
            ease: "easeInOut" 
          }
        };
      case 'float':
        return {
          animate: { 
            y: [0, -15, 0],
            x: [0, 5, 0],
            rotateZ: [0, 10, 0]
          },
          transition: { 
            duration: 3, 
            repeat: Infinity, 
            ease: "easeInOut" 
          }
        };
      case 'wave':
        return {
          animate: { 
            rotateZ: [0, 20, -20, 0],
            scale: [1, 1.1, 1]
          },
          transition: { 
            duration: 1.5, 
            repeat: Infinity, 
            ease: "easeInOut" 
          }
        };
      default:
        return {};
    }
  };

  return (
    <div className="flex justify-center items-center space-x-8 mb-8">
      {icons.map(({ Icon, delay, color, animation }, index) => (
        <motion.div
          key={index}
          initial={{ 
            opacity: 0, 
            scale: 0,
            rotateY: -180
          }}
          animate={{ 
            opacity: 1, 
            scale: 1,
            rotateY: 0
          }}
          transition={{ 
            duration: 0.8, 
            delay,
            type: "spring",
            stiffness: 100
          }}
          className="relative"
        >
          <motion.div
            {...getAnimationProps(animation)}
            whileHover={{ 
              scale: 1.3,
              rotateY: 180,
              transition: { duration: 0.3 }
            }}
            className={`
              p-4 rounded-2xl glass-effect border border-white/20
              ${color} hover:shadow-2xl hover:shadow-current/20
              cursor-pointer transform-gpu
            `}
            style={{
              transformStyle: 'preserve-3d',
              backfaceVisibility: 'hidden'
            }}
          >
            <Icon className="w-8 h-8 sm:w-10 sm:h-10" />
            
            {/* 3D Shadow Effect */}
            <div 
              className="absolute inset-0 rounded-2xl bg-current opacity-10 blur-sm transform translate-x-1 translate-y-1 -z-10"
            />
          </motion.div>

          {/* Floating particles around icons */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: delay + 1 }}
            className="absolute inset-0 pointer-events-none"
          >
            {[...Array(3)].map((_, particleIndex) => (
              <motion.div
                key={particleIndex}
                className="absolute w-1 h-1 bg-current rounded-full opacity-60"
                animate={{
                  x: [0, Math.random() * 40 - 20],
                  y: [0, Math.random() * 40 - 20],
                  opacity: [0.6, 0, 0.6]
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  delay: particleIndex * 0.5,
                  ease: "easeInOut"
                }}
                style={{
                  left: '50%',
                  top: '50%',
                  transform: 'translate(-50%, -50%)'
                }}
              />
            ))}
          </motion.div>
        </motion.div>
      ))}
    </div>
  );
};

export default AnimatedIcons;
