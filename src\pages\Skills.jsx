import React from 'react';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import PageTransition from '../components/PageTransition';

const Skills = () => {
  const [ref, inView] = useInView({
    threshold: 0.1,
    triggerOnce: true
  });

  const skillCategories = [
    {
      title: "Frontend",
      color: "from-blue-400 to-blue-600",
      skills: [
        { name: "React", level: 95, icon: "⚛️" },
        { name: "JavaScript", level: 90, icon: "🟨" },
        { name: "TypeScript", level: 85, icon: "🔷" },
        { name: "Tailwind CSS", level: 90, icon: "🎨" },
        { name: "Vue.js", level: 80, icon: "💚" },
        { name: "HTML/CSS", level: 95, icon: "🌐" }
      ]
    },
    {
      title: "Backend",
      color: "from-purple-400 to-purple-600",
      skills: [
        { name: "Node.js", level: 88, icon: "🟢" },
        { name: "Python", level: 85, icon: "🐍" },
        { name: "Express.js", level: 90, icon: "🚀" },
        { name: "MongoDB", level: 80, icon: "🍃" },
        { name: "PostgreSQL", level: 75, icon: "🐘" },
        { name: "REST APIs", level: 92, icon: "🔗" }
      ]
    },
    {
      title: "Tools & Technologies",
      color: "from-pink-400 to-pink-600",
      skills: [
        { name: "Git", level: 90, icon: "📝" },
        { name: "Docker", level: 75, icon: "🐳" },
        { name: "AWS", level: 70, icon: "☁️" },
        { name: "Figma", level: 85, icon: "🎨" },
        { name: "VS Code", level: 95, icon: "💻" },
        { name: "Webpack", level: 80, icon: "📦" }
      ]
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.3,
        delayChildren: 0.2
      }
    }
  };

  const categoryVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        ease: "easeOut"
      }
    }
  };

  const skillVariants = {
    hidden: { opacity: 0, x: -50 },
    visible: {
      opacity: 1,
      x: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  };

  const SkillBar = ({ skill, index, categoryColor }) => (
    <motion.div
      variants={skillVariants}
      whileHover={{ scale: 1.02, x: 10 }}
      className="group"
    >
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center space-x-3">
          <motion.span
            whileHover={{ 
              scale: 1.3, 
              rotate: 360,
              transition: { duration: 0.3 }
            }}
            className="text-2xl"
          >
            {skill.icon}
          </motion.span>
          <span className="text-white font-medium">{skill.name}</span>
        </div>
        <span className="text-gray-400 text-sm font-medium">{skill.level}%</span>
      </div>
      
      <div className="relative h-2 bg-white/10 rounded-full overflow-hidden">
        <motion.div
          initial={{ width: 0 }}
          animate={inView ? { width: `${skill.level}%` } : { width: 0 }}
          transition={{ 
            duration: 1.5, 
            delay: index * 0.1,
            ease: "easeOut"
          }}
          className={`h-full bg-gradient-to-r ${categoryColor} rounded-full relative`}
        >
          {/* Animated glow effect */}
          <motion.div
            animate={{ x: [-20, 100, -20] }}
            transition={{ 
              duration: 2, 
              repeat: Infinity, 
              ease: "easeInOut",
              delay: index * 0.2
            }}
            className="absolute inset-0 bg-white/30 rounded-full blur-sm"
            style={{ width: '20px' }}
          />
        </motion.div>
      </div>
    </motion.div>
  );

  return (
    <PageTransition variant="rotate">
      <div className="min-h-screen py-20 px-4 sm:px-6 lg:px-8">
        {/* Background Effects */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-1/4 left-1/3 w-96 h-96 bg-accent-500/10 rounded-full blur-3xl animate-pulse-slow" />
          <div className="absolute bottom-1/3 right-1/3 w-96 h-96 bg-primary-500/10 rounded-full blur-3xl animate-pulse-slow" style={{ animationDelay: '1s' }} />
        </div>

        <div className="max-w-6xl mx-auto relative z-10">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: -50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold mb-6">
              <span className="gradient-text">My Skills</span>
            </h1>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
              A comprehensive overview of my technical expertise and the tools I use 
              to bring ideas to life.
            </p>
            <div className="w-24 h-1 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full mx-auto mt-8" />
          </motion.div>

          {/* Skills Categories */}
          <motion.div
            ref={ref}
            variants={containerVariants}
            initial="hidden"
            animate={inView ? "visible" : "hidden"}
            className="space-y-12"
          >
            {skillCategories.map((category, categoryIndex) => (
              <motion.div
                key={category.title}
                variants={categoryVariants}
                className="glass-effect rounded-2xl p-8 border border-white/10"
              >
                <motion.h2
                  whileHover={{ scale: 1.05 }}
                  className="text-2xl sm:text-3xl font-bold mb-8 text-center"
                >
                  <span className={`bg-gradient-to-r ${category.color} bg-clip-text text-transparent`}>
                    {category.title}
                  </span>
                </motion.h2>
                
                <motion.div
                  variants={{
                    hidden: { opacity: 0 },
                    visible: {
                      opacity: 1,
                      transition: {
                        staggerChildren: 0.1,
                        delayChildren: 0.2
                      }
                    }
                  }}
                  className="grid grid-cols-1 md:grid-cols-2 gap-6"
                >
                  {category.skills.map((skill, skillIndex) => (
                    <SkillBar 
                      key={skill.name} 
                      skill={skill} 
                      index={skillIndex}
                      categoryColor={category.color}
                    />
                  ))}
                </motion.div>
              </motion.div>
            ))}
          </motion.div>

          {/* Additional Skills Section */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
            transition={{ duration: 0.8, delay: 1 }}
            className="mt-16 text-center"
          >
            <h3 className="text-2xl font-bold text-white mb-6">Always Learning</h3>
            <p className="text-gray-300 max-w-2xl mx-auto mb-8">
              Technology evolves rapidly, and I'm committed to continuous learning. 
              Currently exploring: AI/ML, Web3, and Advanced React Patterns.
            </p>
            
            <div className="flex flex-wrap justify-center gap-4">
              {["AI/ML", "Web3", "GraphQL", "Next.js", "React Native", "Kubernetes"].map((tech, index) => (
                <motion.span
                  key={tech}
                  initial={{ opacity: 0, scale: 0 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 1.5 + index * 0.1 }}
                  whileHover={{ 
                    scale: 1.1,
                    rotate: [0, -5, 5, 0],
                    transition: { duration: 0.3 }
                  }}
                  className="px-4 py-2 text-sm font-medium bg-gradient-to-r from-primary-500/20 to-secondary-500/20 text-white rounded-full border border-white/20 hover:border-primary-400/50 transition-all duration-300"
                >
                  {tech}
                </motion.span>
              ))}
            </div>
          </motion.div>
        </div>
      </div>
    </PageTransition>
  );
};

export default Skills;
