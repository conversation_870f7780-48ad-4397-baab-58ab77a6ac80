{"name": "prtfo", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview"}, "devDependencies": {"autoprefixer": "^10.4.21", "postcss": "^8.4.49", "tailwindcss": "^3.4.17", "typescript": "~5.8.3", "vite": "^7.1.2"}, "dependencies": {"@heroicons/react": "^2.2.0", "framer-motion": "^12.23.12", "lucide-react": "^0.539.0", "react": "^19.1.1", "react-dom": "^19.1.1", "react-hook-form": "^7.62.0", "react-intersection-observer": "^9.16.0", "react-router-dom": "^7.8.0"}}