import React from 'react';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import PageTransition from '../components/PageTransition';

const About = () => {
  const [ref, inView] = useInView({
    threshold: 0.1,
    triggerOnce: true
  });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.3,
        delayChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        ease: "easeOut"
      }
    }
  };

  const imageVariants = {
    hidden: { opacity: 0, scale: 0.8, rotateY: -30 },
    visible: {
      opacity: 1,
      scale: 1,
      rotateY: 0,
      transition: {
        duration: 1,
        ease: "easeOut"
      }
    }
  };

  return (
    <PageTransition variant="slide">
      <div className="min-h-screen py-20 px-4 sm:px-6 lg:px-8">
        {/* Gradient Blur Background */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-1/3 left-1/6 w-96 h-96 bg-purple-500/15 rounded-full blur-3xl animate-pulse" />
          <div className="absolute bottom-1/3 right-1/6 w-96 h-96 bg-blue-500/15 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }} />
        </div>

        <div className="max-w-6xl mx-auto relative z-10">
          <motion.div
            ref={ref}
            variants={containerVariants}
            initial="hidden"
            animate={inView ? "visible" : "hidden"}
            className="grid lg:grid-cols-2 gap-12 items-center"
          >
            {/* Profile Image Section */}
            <motion.div
              variants={imageVariants}
              className="flex justify-center lg:justify-start"
            >
              <div className="relative">
                <motion.div
                  whileHover={{ scale: 1.05, rotateY: 10 }}
                  transition={{ duration: 0.3 }}
                  className="relative w-80 h-80 rounded-2xl overflow-hidden glass-effect border border-white/20"
                  style={{ transformStyle: 'preserve-3d' }}
                >
                  {/* Placeholder for profile image */}
                  <div className="w-full h-full bg-gradient-to-br from-primary-500/20 to-secondary-500/20 flex items-center justify-center">
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                      className="w-32 h-32 rounded-full bg-gradient-to-r from-primary-400 to-secondary-400 flex items-center justify-center text-4xl font-bold text-white"
                    >
                      YN
                    </motion.div>
                  </div>
                  
                  {/* Animated border */}
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
                    className="absolute inset-0 rounded-2xl border-2 border-transparent bg-gradient-to-r from-primary-400 via-secondary-400 to-accent-400 p-[2px]"
                    style={{ mask: 'linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)', maskComposite: 'exclude' }}
                  />
                </motion.div>

                {/* Floating elements around image */}
                {[...Array(6)].map((_, index) => (
                  <motion.div
                    key={index}
                    className="absolute w-2 h-2 bg-primary-400 rounded-full"
                    animate={{
                      x: [0, Math.cos(index * 60 * Math.PI / 180) * 50],
                      y: [0, Math.sin(index * 60 * Math.PI / 180) * 50],
                      opacity: [0.3, 1, 0.3]
                    }}
                    transition={{
                      duration: 4,
                      repeat: Infinity,
                      delay: index * 0.3,
                      ease: "easeInOut"
                    }}
                    style={{
                      left: '50%',
                      top: '50%',
                      transform: 'translate(-50%, -50%)'
                    }}
                  />
                ))}
              </div>
            </motion.div>

            {/* Content Section */}
            <motion.div variants={itemVariants} className="space-y-8">
              <motion.div variants={itemVariants}>
                <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold mb-6">
                  <span className="gradient-text">About Me</span>
                </h1>
                <div className="w-24 h-1 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full" />
              </motion.div>

              <motion.div variants={itemVariants} className="space-y-6 text-lg text-gray-300 leading-relaxed">
                <p>
                  Hello! I'm a passionate full-stack developer with a love for creating 
                  beautiful, functional, and user-centered digital experiences. With over 
                  X years of experience in web development, I specialize in modern 
                  JavaScript frameworks and cutting-edge technologies.
                </p>
                
                <p>
                  My journey in tech started with curiosity and has evolved into a 
                  career focused on solving complex problems through elegant code. 
                  I believe in writing clean, maintainable code and creating 
                  interfaces that users love to interact with.
                </p>
                
                <p>
                  When I'm not coding, you can find me exploring new technologies, 
                  contributing to open-source projects, or sharing my knowledge 
                  through technical writing and mentoring.
                </p>
              </motion.div>

              <motion.div variants={itemVariants} className="space-y-4">
                <h3 className="text-2xl font-semibold text-white">Quick Facts</h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  {[
                    { label: 'Location', value: 'Your City, Country' },
                    { label: 'Experience', value: 'X+ Years' },
                    { label: 'Projects', value: '50+ Completed' },
                    { label: 'Coffee Consumed', value: '∞ Cups' }
                  ].map((fact, index) => (
                    <motion.div
                      key={fact.label}
                      whileHover={{ scale: 1.05 }}
                      className="p-4 glass-effect rounded-lg border border-white/10"
                    >
                      <div className="text-sm text-gray-400">{fact.label}</div>
                      <div className="text-lg font-semibold text-white">{fact.value}</div>
                    </motion.div>
                  ))}
                </div>
              </motion.div>

              <motion.div variants={itemVariants}>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="inline-flex items-center px-6 py-3 text-lg font-semibold text-white bg-gradient-to-r from-blue-500 to-purple-500 rounded-full hover:from-blue-600 hover:to-purple-600 transition-all duration-300 neon-glow"
                >
                  Download Resume
                  <motion.span
                    animate={{ x: [0, 5, 0] }}
                    transition={{ repeat: Infinity, duration: 1.5 }}
                    className="ml-2"
                  >
                    ↓
                  </motion.span>
                </motion.button>
              </motion.div>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </PageTransition>
  );
};

export default About;
