# 🚀 Dark-Themed Animated Portfolio Website

A modern, heavily animated personal portfolio website built with React.js, Tailwind CSS v3.4.17, and Framer Motion.

## ✨ Features

### 🎨 Design & Animations
- **Dark Theme**: Consistent dark color scheme with neon gradient accents
- **3D Animations**: Transform-based hover effects and rotating elements
- **Particle Systems**: Interactive particle backgrounds that react to mouse movement
- **Glass Morphism**: Frosted glass effects throughout the interface
- **Gradient Effects**: Beautiful gradient backgrounds and text effects
- **Page Transitions**: Seamless animated transitions between pages

### 📱 Pages & Sections

#### 🏠 Home/Hero Section
- Fullscreen hero with animated gradient backgrounds
- 3D animated icons (rotating cube, bouncing code bracket, floating rocket)
- Interactive particle background
- Staggered text animations with gradient effects
- Animated CTA buttons with hover effects
- Scroll indicator animation

#### 👤 About Me Page
- Animated profile image with 3D hover effects
- Scroll-triggered text animations
- Quick facts cards with hover animations
- Floating particle effects around profile image
- Download resume button

#### 💼 Projects Page
- Responsive grid layout with featured project support
- 3D hover animations for project cards
- Tech stack display with animated tags
- Live demo and GitHub links
- Project categories and featured badges

#### 🛠️ Skills Page
- Animated skill progress bars with glow effects
- Categorized sections (Frontend, Backend, Tools)
- Interactive skill icons with bounce/rotate animations
- Skill level indicators with smooth animations

#### 📄 Resume Page
- Downloadable PDF button with animations
- Modal preview with scale-in animation
- Professional resume layout preview
- Quick statistics display

#### 📧 Contact Page
- Contact form with real-time validation
- Animated send button with loading states
- Contact information cards with hover effects
- Form submission handling with success feedback

#### 📝 Featured Articles Page
- Animated blog post cards
- Hover effects revealing excerpts
- Category tags and featured badges
- Newsletter subscription CTA

#### 💻 Coding Profiles Page
- Animated platform logos (LeetCode, Codeforces, Codewars, etc.)
- 3D hover effects with rotation
- Statistics display for each platform
- Achievement summary section

#### 🔗 Social Links Component
- Fixed position social media icons
- 3D rotating hover animations
- Staggered entrance animations
- Color-coded platform icons

### 🛠️ Technical Features
- **React.js**: Modern React with hooks and functional components
- **Tailwind CSS v3.4.17**: Utility-first CSS framework
- **Framer Motion**: Advanced animations and page transitions
- **React Router**: Client-side routing with animated transitions
- **React Hook Form**: Form handling with validation
- **Responsive Design**: Mobile-first approach
- **Performance Optimized**: Lazy loading and optimized animations

## 🚀 Getting Started

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd prtfo
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open your browser and navigate to `http://localhost:5173`

### Build for Production

```bash
npm run build
```

## 📦 Dependencies

### Core Dependencies
- **react**: ^19.1.1
- **react-dom**: ^19.1.1
- **react-router-dom**: ^7.8.0
- **framer-motion**: ^12.23.12
- **react-hook-form**: ^7.62.0
- **react-intersection-observer**: ^9.16.0
- **@heroicons/react**: ^2.2.0
- **lucide-react**: ^0.539.0

### Dev Dependencies
- **tailwindcss**: ^3.4.17
- **postcss**: ^8.4.49
- **autoprefixer**: ^10.4.21
- **vite**: ^7.1.2
- **typescript**: ~5.8.3

## 🎯 Customization

### Personal Information
Update the following files with your personal information:
- `src/pages/Home.jsx` - Name and introduction
- `src/pages/About.jsx` - Bio and personal details
- `src/pages/Projects.jsx` - Your projects and links
- `src/pages/Skills.jsx` - Your skills and proficiency levels
- `src/components/SocialLinks.jsx` - Your social media links
- `src/pages/CodingProfiles.jsx` - Your coding platform profiles

### Colors and Styling
The color scheme can be customized in:
- `tailwind.config.js` - Custom color palette
- `src/style.css` - Custom CSS classes and animations

### Content
- Replace placeholder project images with actual screenshots
- Update project descriptions and tech stacks
- Add your actual resume PDF
- Update contact information

## 🌟 Features Highlights

- ✅ Fully responsive design
- ✅ Dark theme with neon accents
- ✅ 3D hover animations
- ✅ Interactive particle backgrounds
- ✅ Smooth page transitions
- ✅ Form validation
- ✅ Mobile-optimized animations
- ✅ Performance optimized
- ✅ SEO-friendly structure
- ✅ Modern React patterns

## 📱 Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

## 🤝 Contributing

Feel free to fork this project and customize it for your own portfolio!

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

---

**Built with ❤️ using React.js, Tailwind CSS, and Framer Motion**
