import React from 'react';
import { motion } from 'framer-motion';
import { 
  Github, 
  Linkedin, 
  Twitter, 
  Instagram, 
  Youtube, 
  Mail,
  ExternalLink
} from 'lucide-react';

const SocialLinks = () => {
  const socialLinks = [
    {
      name: 'GitHub',
      url: 'https://github.com/yourusername',
      icon: Github,
      color: 'hover:text-gray-400',
      bgColor: 'hover:bg-gray-400/20'
    },
    {
      name: 'LinkedIn',
      url: 'https://linkedin.com/in/yourusername',
      icon: Linkedin,
      color: 'hover:text-blue-400',
      bgColor: 'hover:bg-blue-400/20'
    },
    {
      name: 'Twitter',
      url: 'https://twitter.com/yourusername',
      icon: Twitter,
      color: 'hover:text-sky-400',
      bgColor: 'hover:bg-sky-400/20'
    },
    {
      name: 'Instagram',
      url: 'https://instagram.com/yourusername',
      icon: Instagram,
      color: 'hover:text-pink-400',
      bgColor: 'hover:bg-pink-400/20'
    },
    {
      name: 'YouTube',
      url: 'https://youtube.com/@yourusername',
      icon: Youtube,
      color: 'hover:text-red-400',
      bgColor: 'hover:bg-red-400/20'
    },
    {
      name: 'Email',
      url: 'mailto:<EMAIL>',
      icon: Mail,
      color: 'hover:text-green-400',
      bgColor: 'hover:bg-green-400/20'
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 1
      }
    }
  };

  const itemVariants = {
    hidden: { 
      opacity: 0, 
      x: -50,
      rotate: -180
    },
    visible: { 
      opacity: 1, 
      x: 0,
      rotate: 0,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 10
      }
    }
  };

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="fixed left-6 top-1/2 transform -translate-y-1/2 z-40 hidden lg:block"
    >
      <div className="flex flex-col space-y-4">
        {socialLinks.map((link, index) => {
          const IconComponent = link.icon;
          return (
            <motion.a
              key={link.name}
              href={link.url}
              target="_blank"
              rel="noopener noreferrer"
              variants={itemVariants}
              whileHover={{ 
                scale: 1.2, 
                rotate: 360,
                transition: { duration: 0.3 }
              }}
              whileTap={{ scale: 0.9 }}
              className={`
                p-3 rounded-full glass-effect border border-white/10 
                text-gray-400 transition-all duration-300 group
                ${link.color} ${link.bgColor}
                hover:shadow-lg hover:shadow-current/20
              `}
              title={link.name}
            >
              <IconComponent className="w-5 h-5" />
            </motion.a>
          );
        })}
        
        {/* Decorative line */}
        <motion.div
          initial={{ height: 0 }}
          animate={{ height: 60 }}
          transition={{ delay: 1.5, duration: 0.8 }}
          className="w-px bg-gradient-to-b from-primary-400 to-transparent mx-auto"
        />
      </div>
    </motion.div>
  );
};

export default SocialLinks;
