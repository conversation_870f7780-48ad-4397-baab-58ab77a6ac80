import React from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { ChevronDownIcon, ArrowTopRightOnSquareIcon, CodeBracketIcon, DocumentArrowDownIcon, EnvelopeIcon } from '@heroicons/react/24/outline';
import { useInView } from 'react-intersection-observer';
import PageTransition from '../components/PageTransition';
import ParticleBackground from '../components/ParticleBackground';
import AnimatedIcons from '../components/AnimatedIcons';


const Home = () => {
  // Intersection Observer hooks for each section
  const [heroRef, heroInView] = useInView({ threshold: 0.3, triggerOnce: true });
  const [aboutRef, aboutInView] = useInView({ threshold: 0.3, triggerOnce: true });
  const [projectsRef, projectsInView] = useInView({ threshold: 0.2, triggerOnce: true });
  const [skillsRef, skillsInView] = useInView({ threshold: 0.3, triggerOnce: true });
  const [contactRef, contactInView] = useInView({ threshold: 0.3, triggerOnce: true });

  const sectionVariants = {
    hidden: { opacity: 0, y: 60 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        ease: "easeOut"
      }
    }
  };

  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3
      }
    }
  };

  // Sample data
  const featuredProjects = [
    {
      title: "E-Commerce Platform",
      description: "Full-stack e-commerce solution with React, Node.js, and MongoDB",
      techStack: ["React", "Node.js", "MongoDB", "Stripe"],
      liveUrl: "https://example.com",
      githubUrl: "https://github.com/username/project"
    },
    {
      title: "Task Management App",
      description: "Collaborative task management with real-time updates",
      techStack: ["Vue.js", "Firebase", "Socket.io"],
      liveUrl: "https://example.com",
      githubUrl: "https://github.com/username/project"
    },
    {
      title: "Weather Dashboard",
      description: "Interactive weather dashboard with data visualization",
      techStack: ["React", "D3.js", "OpenWeather API"],
      liveUrl: "https://example.com",
      githubUrl: "https://github.com/username/project"
    }
  ];

  const skills = [
    { name: "React", level: 95, icon: "⚛️" },
    { name: "JavaScript", level: 90, icon: "🟨" },
    { name: "Node.js", level: 88, icon: "🟢" },
    { name: "Python", level: 85, icon: "🐍" },
    { name: "MongoDB", level: 80, icon: "🍃" },
    { name: "AWS", level: 75, icon: "☁️" }
  ];

  return (
    <PageTransition variant="blur">
      <div className="relative">
        {/* Hero Section */}
        <section ref={heroRef} className="relative min-h-screen flex items-center justify-center overflow-hidden">
          {/* Particle Background */}
          <ParticleBackground />
          
          {/* Gradient Blur Backgrounds */}
          <div className="absolute inset-0 overflow-hidden">
            <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-500/20 rounded-full blur-3xl animate-pulse" />
            <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-purple-500/20 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1s' }} />
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-pink-500/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }} />
          </div>

          {/* Hero Content */}
          <div className="relative z-10 px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center min-h-screen">

              {/* Left Side - Text Content */}
              <div className="text-center lg:text-left">

           

            {/* Hero Text */}
            <motion.div
              variants={staggerContainer}
              initial="hidden"
              animate="visible"
              className="space-y-8"
            >
              <motion.h1 
                className="text-5xl sm:text-6xl lg:text-8xl font-bold leading-tight"
                variants={sectionVariants}
              >
                <span className="block text-white mb-4">Hi, I'm</span>
                <span className="block gradient-text text-shadow-lg">Mayank Raj Anand</span>
              </motion.h1>

              <motion.p 
                className="text-2xl sm:text-3xl lg:text-4xl text-gray-300 font-light max-w-4xl mx-auto leading-relaxed"
                variants={sectionVariants}
              >
                Full Stack Developer & Creative Technologist
              </motion.p>

              <motion.p 
                className="text-lg sm:text-xl text-gray-400 max-w-3xl mx-auto leading-relaxed"
                variants={sectionVariants}
              >
                I craft exceptional digital experiences through innovative design and cutting-edge technology.
              </motion.p>

              {/* CTA Buttons */}
              <motion.div
                variants={sectionVariants}
                className="flex flex-col sm:flex-row gap-4 justify-center items-center pt-8"
              >
                <Link
                  to="/contact"
                  className="inline-flex items-center px-8 py-4 text-lg font-semibold text-white bg-gradient-to-r from-blue-500 to-purple-500 rounded-full hover:from-blue-600 hover:to-purple-600 transition-all duration-300 neon-glow"
                >
                  Let's Work Together
                  <ArrowTopRightOnSquareIcon className="w-5 h-5 ml-2" />
                </Link>
                <Link
                  to="/projects"
                  className="inline-flex items-center px-8 py-4 text-lg font-semibold text-white border-2 border-white/20 rounded-full hover:border-blue-400 hover:bg-white/5 transition-all duration-300 glass-effect"
                >
                  View My Work
                </Link>
              </motion.div>
            </motion.div>
              </div>

              {/* Right Side - Profile Image */}
              <div className="flex justify-center lg:justify-end">
                <motion.div
                  initial={{ opacity: 0, scale: 0, rotateY: 180 }}
                  animate={{ opacity: 1, scale: 1, rotateY: 0 }}
                  transition={{ duration: 1, delay: 0.3, type: "spring", stiffness: 100 }}
                  className="relative"
                >
                  <motion.div
                    whileHover={{ scale: 1.1, rotateY: 10 }}
                    transition={{ duration: 0.3 }}
                    className="relative w-64 h-64 sm:w-80 sm:h-80 lg:w-96 lg:h-96 rounded-full overflow-hidden glass-effect border-4 border-white/20"
                    style={{ transformStyle: 'preserve-3d' }}
                  >
                    {/* Profile Image */}
                    <div className="w-full h-full">
                      <img
                        src="/por.jpg"
                        alt="Mayank Raj Anand - Full Stack Developer"
                        className="w-full h-full object-cover"
                      />
                    </div>

                    {/* Animated border */}
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
                      className="absolute inset-0 rounded-full border-2 border-transparent bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 p-[2px]"
                      style={{ mask: 'linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)', maskComposite: 'exclude' }}
                    />
                  </motion.div>

                  {/* Floating particles around profile */}
                  {[...Array(8)].map((_, index) => (
                    <motion.div
                      key={index}
                      className="absolute w-3 h-3 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full"
                      animate={{
                        x: [0, Math.cos(index * 45 * Math.PI / 180) * 80],
                        y: [0, Math.sin(index * 45 * Math.PI / 180) * 80],
                        opacity: [0.3, 1, 0.3],
                        scale: [0.8, 1.2, 0.8]
                      }}
                      transition={{
                        duration: 5,
                        repeat: Infinity,
                        delay: index * 0.4,
                        ease: "easeInOut"
                      }}
                      style={{
                        left: '50%',
                        top: '50%',
                        transform: 'translate(-50%, -50%)'
                      }}
                    />
                  ))}
                </motion.div>
              </div>
            </div>
          </div>

          {/* Animated Icons - Below Navbar Area */}
          <motion.div
            initial={{ opacity: 0, y: -30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 0.5 }}
            className="absolute top-20 left-1/2 transform -translate-x-1/2 z-10"
          >
            <motion.div
              whileHover={{ scale: 1.1 }}
              transition={{ duration: 0.3 }}
              className="flex justify-center"
            >
              <AnimatedIcons />
            </motion.div>
          </motion.div>

          {/* Scroll Indicator */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 2, duration: 0.8 }}
            className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
          >
            <motion.div
              animate={{ y: [0, 10, 0] }}
              transition={{ repeat: Infinity, duration: 2 }}
              className="flex flex-col items-center text-gray-400"
            >
              <br></br>
              <ChevronDownIcon className="w-6 h-6" />
              <ChevronDownIcon className="w-6 h-6" />
            </motion.div>
          </motion.div>
        </section>

        {/* About Section */}
        <section ref={aboutRef} className="py-20 px-4 sm:px-6 lg:px-8 bg-slate-900/50">
          <div className="max-w-6xl mx-auto">
            <motion.div
              variants={sectionVariants}
              initial="hidden"
              animate={aboutInView ? "visible" : "hidden"}
              className="text-center mb-16"
            >
              <h2 className="text-4xl sm:text-5xl font-bold mb-6">
                <span className="gradient-text">About Me</span>
              </h2>
              <div className="w-24 h-1 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full mx-auto" />
            </motion.div>

            <motion.div
              variants={staggerContainer}
              initial="hidden"
              animate={aboutInView ? "visible" : "hidden"}
              className="grid lg:grid-cols-2 gap-12 items-center"
            >
              <motion.div variants={sectionVariants} className="space-y-6">
                <p className="text-lg text-gray-300 leading-relaxed">
                  I'm a passionate full-stack developer with over 3 years of experience creating 
                  digital solutions that make a difference. I specialize in modern web technologies 
                  and love turning complex problems into simple, beautiful designs.
                </p>
                <p className="text-lg text-gray-300 leading-relaxed">
                  When I'm not coding, you can find me exploring new technologies, contributing to 
                  open-source projects, or sharing knowledge through technical writing and mentoring.
                </p>
                <div className="grid grid-cols-2 gap-4 pt-6">
                  {[
                    { label: 'Projects', value: '50+' },
                    { label: 'Experience', value: '3+ Years' },
                    { label: 'Technologies', value: '20+' },
                    { label: 'Satisfaction', value: '100%' }
                  ].map((stat, index) => (
                    <motion.div
                      key={stat.label}
                      variants={sectionVariants}
                      className="text-center p-4 glass-effect rounded-lg border border-white/10"
                    >
                      <div className="text-2xl font-bold gradient-text mb-1">{stat.value}</div>
                      <div className="text-sm text-gray-400">{stat.label}</div>
                    </motion.div>
                  ))}
                </div>
              </motion.div>

              <motion.div variants={sectionVariants} className="flex justify-center">
                <div className="relative w-80 h-80 rounded-2xl overflow-hidden glass-effect border border-white/20">
                  <div className="w-full h-full bg-gradient-to-br from-blue-500/20 to-purple-500/20 flex items-center justify-center">
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                      className="w-32 h-32 rounded-full bg-gradient-to-r from-blue-400 to-purple-400 flex items-center justify-center text-4xl font-bold text-white"
                    >
                      AJ
                    </motion.div>
                  </div>
                </div>
              </motion.div>
            </motion.div>
          </div>
        </section>

        {/* Projects Section */}
        <section ref={projectsRef} className="py-20 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto">
            <motion.div
              variants={sectionVariants}
              initial="hidden"
              animate={projectsInView ? "visible" : "hidden"}
              className="text-center mb-16"
            >
              <h2 className="text-4xl sm:text-5xl font-bold mb-6">
                <span className="gradient-text">Featured Projects</span>
              </h2>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                Here are some of my recent projects that showcase my skills and passion for development.
              </p>
              <div className="w-24 h-1 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full mx-auto mt-8" />
            </motion.div>

            <motion.div
              variants={staggerContainer}
              initial="hidden"
              animate={projectsInView ? "visible" : "hidden"}
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
            >
              {featuredProjects.map((project, index) => (
                <motion.div
                  key={project.title}
                  variants={sectionVariants}
                  whileHover={{ y: -10, scale: 1.02 }}
                  className="glass-effect rounded-2xl overflow-hidden border border-white/10 hover:border-blue-400/50 transition-all duration-300 group"
                >
                  <div className="h-48 bg-gradient-to-br from-blue-500/20 to-purple-500/20 flex items-center justify-center">
                    <motion.div
                      animate={{ rotate: [0, 360] }}
                      transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                      className="w-16 h-16 border-4 border-blue-400 border-t-transparent rounded-full"
                    />
                  </div>
                  
                  <div className="p-6">
                    <h3 className="text-xl font-bold text-white mb-3 group-hover:text-blue-400 transition-colors">
                      {project.title}
                    </h3>
                    <p className="text-gray-300 mb-4">{project.description}</p>
                    
                    <div className="flex flex-wrap gap-2 mb-4">
                      {project.techStack.map((tech) => (
                        <span key={tech} className="px-3 py-1 text-xs bg-white/10 text-gray-300 rounded-full">
                          {tech}
                        </span>
                      ))}
                    </div>
                    
                    <div className="flex space-x-4">
                      <a href={project.liveUrl} className="flex items-center text-blue-400 hover:text-blue-300">
                        <ArrowTopRightOnSquareIcon className="w-4 h-4 mr-1" />
                        Live Demo
                      </a>
                      <a href={project.githubUrl} className="flex items-center text-gray-400 hover:text-gray-300">
                        <CodeBracketIcon className="w-4 h-4 mr-1" />
                        Code
                      </a>
                    </div>
                  </div>
                </motion.div>
              ))}
            </motion.div>

            <motion.div
              variants={sectionVariants}
              initial="hidden"
              animate={projectsInView ? "visible" : "hidden"}
              className="text-center mt-12"
            >
              <Link
                to="/projects"
                className="inline-flex items-center px-6 py-3 text-lg font-semibold text-white bg-gradient-to-r from-blue-500 to-purple-500 rounded-full hover:from-blue-600 hover:to-purple-600 transition-all duration-300"
              >
                View All Projects
                <ArrowTopRightOnSquareIcon className="w-5 h-5 ml-2" />
              </Link>
            </motion.div>
          </div>
        </section>

        {/* Skills Section */}
        <section ref={skillsRef} className="py-20 px-4 sm:px-6 lg:px-8 bg-slate-900/50">
          <div className="max-w-6xl mx-auto">
            <motion.div
              variants={sectionVariants}
              initial="hidden"
              animate={skillsInView ? "visible" : "hidden"}
              className="text-center mb-16"
            >
              <h2 className="text-4xl sm:text-5xl font-bold mb-6">
                <span className="gradient-text">My Skills</span>
              </h2>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                Technologies and tools I use to bring ideas to life.
              </p>
              <div className="w-24 h-1 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full mx-auto mt-8" />
            </motion.div>

            <motion.div
              variants={staggerContainer}
              initial="hidden"
              animate={skillsInView ? "visible" : "hidden"}
              className="grid grid-cols-1 md:grid-cols-2 gap-8"
            >
              {skills.map((skill, index) => (
                <motion.div
                  key={skill.name}
                  variants={sectionVariants}
                  whileHover={{ scale: 1.02, x: 10 }}
                  className="group"
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-3">
                      <motion.span
                        whileHover={{ scale: 1.3, rotate: 360 }}
                        className="text-2xl"
                      >
                        {skill.icon}
                      </motion.span>
                      <span className="text-white font-medium">{skill.name}</span>
                    </div>
                    <span className="text-gray-400 text-sm font-medium">{skill.level}%</span>
                  </div>

                  <div className="relative h-2 bg-white/10 rounded-full overflow-hidden">
                    <motion.div
                      initial={{ width: 0 }}
                      animate={skillsInView ? { width: `${skill.level}%` } : { width: 0 }}
                      transition={{ duration: 1.5, delay: index * 0.1, ease: "easeOut" }}
                      className="h-full bg-gradient-to-r from-blue-400 to-purple-400 rounded-full"
                    />
                  </div>
                </motion.div>
              ))}
            </motion.div>

            <motion.div
              variants={sectionVariants}
              initial="hidden"
              animate={skillsInView ? "visible" : "hidden"}
              className="text-center mt-12"
            >
              <Link
                to="/skills"
                className="inline-flex items-center px-6 py-3 text-lg font-semibold text-white border-2 border-white/20 rounded-full hover:border-blue-400 hover:bg-white/5 transition-all duration-300"
              >
                View All Skills
                <ArrowTopRightOnSquareIcon className="w-5 h-5 ml-2" />
              </Link>
            </motion.div>
          </div>
        </section>

        {/* Contact Section */}
        <section ref={contactRef} className="py-20 px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <motion.div
              variants={sectionVariants}
              initial="hidden"
              animate={contactInView ? "visible" : "hidden"}
              className="text-center mb-16"
            >
              <h2 className="text-4xl sm:text-5xl font-bold mb-6">
                <span className="gradient-text">Let's Work Together</span>
              </h2>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                Have a project in mind? I'd love to hear about it and discuss how we can bring your ideas to life.
              </p>
              <div className="w-24 h-1 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full mx-auto mt-8" />
            </motion.div>

            <motion.div
              variants={staggerContainer}
              initial="hidden"
              animate={contactInView ? "visible" : "hidden"}
              className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12"
            >
              {[
                { icon: EnvelopeIcon, label: 'Email', value: '<EMAIL>', href: 'mailto:<EMAIL>' },
                { icon: DocumentArrowDownIcon, label: 'Resume', value: 'Download CV', href: '/resume' },
                { icon: CodeBracketIcon, label: 'GitHub', value: 'View Projects', href: 'https://github.com/username' }
              ].map((contact, index) => {
                const IconComponent = contact.icon;
                return (
                  <motion.a
                    key={contact.label}
                    href={contact.href}
                    variants={sectionVariants}
                    whileHover={{ scale: 1.05, y: -5 }}
                    className="flex flex-col items-center p-6 glass-effect rounded-xl border border-white/10 hover:border-blue-400/50 transition-all duration-300 group"
                  >
                    <div className="p-4 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full mb-4 group-hover:scale-110 transition-transform">
                      <IconComponent className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="text-lg font-semibold text-white mb-2">{contact.label}</h3>
                    <p className="text-gray-400 text-center">{contact.value}</p>
                  </motion.a>
                );
              })}
            </motion.div>

            <motion.div
              variants={sectionVariants}
              initial="hidden"
              animate={contactInView ? "visible" : "hidden"}
              className="text-center"
            >
              <Link
                to="/contact"
                className="inline-flex items-center px-8 py-4 text-lg font-semibold text-white bg-gradient-to-r from-blue-500 to-purple-500 rounded-full hover:from-blue-600 hover:to-purple-600 transition-all duration-300 neon-glow"
              >
                Get In Touch
                <EnvelopeIcon className="w-5 h-5 ml-2" />
              </Link>
            </motion.div>
          </div>
        </section>

        {/* Footer */}
        <footer className="py-12 px-4 sm:px-6 lg:px-8 bg-slate-900 border-t border-white/10">
          <div className="max-w-6xl mx-auto">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <div className="mb-4 md:mb-0">
                <h3 className="text-2xl font-bold gradient-text">Alex Johnson</h3>
                <p className="text-gray-400">Full Stack Developer & Creative Technologist</p>
              </div>

              <div className="flex space-x-6">
                {[
                  { name: 'GitHub', url: 'https://github.com/username', icon: '⚡' },
                  { name: 'LinkedIn', url: 'https://linkedin.com/in/username', icon: '💼' },
                  { name: 'Twitter', url: 'https://twitter.com/username', icon: '🐦' },
                  { name: 'Email', url: 'mailto:<EMAIL>', icon: '📧' }
                ].map((social) => (
                  <motion.a
                    key={social.name}
                    href={social.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    whileHover={{ scale: 1.2, y: -2 }}
                    whileTap={{ scale: 0.9 }}
                    className="w-10 h-10 rounded-full glass-effect border border-white/20 flex items-center justify-center hover:border-blue-400/50 transition-all duration-300"
                    title={social.name}
                  >
                    <span className="text-lg">{social.icon}</span>
                  </motion.a>
                ))}
              </div>
            </div>

            <div className="mt-8 pt-8 border-t border-white/10 text-center">
              <p className="text-gray-400">
                © 2024 Alex Johnson. Built with React, Tailwind CSS, and Framer Motion.
              </p>
            </div>
          </div>
        </footer>
      </div>
    </PageTransition>
  );
};

export default Home;
